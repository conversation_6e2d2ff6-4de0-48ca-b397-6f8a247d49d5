import adsk.core
import adsk.fusion
import os
import traceback
import math
from ...lib import fusionAddInUtils as futil
from ... import config

app = adsk.core.Application.get()
ui  = app.userInterface

CMD_ID           = f'{config.COMPANY_NAME}_{config.ADDIN_NAME}_cmdDialog'
CMD_NAME         = 'Slicer'
CMD_Description  = 'Slicer Add-in'
WORKSPACE_ID     = 'FusionSolidEnvironment'
PANEL_ID         = 'SolidScriptsAddinsPanel'
COMMAND_BESIDE_ID= 'ScriptsManagerCommand'
ICON_FOLDER      = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'resources','')

local_handlers = []

def start():
    cmd_def = ui.commandDefinitions.addButtonDefinition(CMD_ID, CMD_NAME, CMD_Description, ICON_FOLDER)
    futil.add_handler(cmd_def.commandCreated, command_created)
    ws = ui.workspaces.itemById(WORKSPACE_ID)
    panel = ws.toolbarPanels.itemById(PANEL_ID)
    ctl = panel.controls.addCommand(cmd_def, COMMAND_BESIDE_ID, False)
    ctl.isPromoted = True

def stop():
    ws = ui.workspaces.itemById(WORKSPACE_ID)
    panel = ws.toolbarPanels.itemById(PANEL_ID)
    ctl = panel.controls.itemById(CMD_ID)
    cmd_def = ui.commandDefinitions.itemById(CMD_ID)
    if ctl: ctl.deleteMe()
    if cmd_def: cmd_def.deleteMe()

def command_created(args: adsk.core.CommandCreatedEventArgs):
    try:
        inputs = args.command.commandInputs

        # 1) Body selector
        inputs.addSelectionInput('body_selection', 'Body', 'Select the body to slice')

        # 2) Ensure we have an active design
        design = adsk.fusion.Design.cast(app.activeProduct)
        if not design:
            ui.messageBox("A design must be active to use this command.", CMD_NAME)
            return
        defaultUnits = design.unitsManager.defaultLengthUnits

        # 3) Axis dropdown
        axis_in = inputs.addDropDownCommandInput('axis', 'Axis',
                      adsk.core.DropDownStyles.TextListDropDownStyle)
        axis_in.listItems.add('X', False)
        axis_in.listItems.add('Y', True)
        axis_in.listItems.add('Z', False)
        axis_in.listItems.add('Radial', False)  # Add radial option

        # Add radial-specific inputs (initially hidden)
        inputs.addValueInput('radial_spacing', 'Angular Spacing (degrees)', '',
                            adsk.core.ValueInput.createByString("10"))
        inputs.addValueInput('radial_start_angle', 'Start Angle (degrees)', '',
                            adsk.core.ValueInput.createByString("0"))
        inputs.addValueInput('radial_end_angle', 'End Angle (degrees)', '',
                            adsk.core.ValueInput.createByString("360"))
        
        # Add center axis selection for radial slicing
        center_axis_in = inputs.addDropDownCommandInput('radial_center_axis', 'Radial Center Axis',
                            adsk.core.DropDownStyles.TextListDropDownStyle)
        center_axis_in.listItems.add('Auto-detect', True)
        center_axis_in.listItems.add('X-axis', False)
        center_axis_in.listItems.add('Y-axis', False)
        center_axis_in.listItems.add('Z-axis', False)

        # 4) Numeric inputs - CREATE THESE FIRST
        unit_abbrev = ""
        if defaultUnits == "mm":
            unit_abbrev = "mm"
            spacing_default = "10 mm"
            thickness_default = "10 mm"
        elif defaultUnits == "cm":
            unit_abbrev = "cm" 
            spacing_default = "1 cm"
            thickness_default = "1 cm"
        elif defaultUnits == "m":
            unit_abbrev = "m"
            spacing_default = "0.01 m"
            thickness_default = "0.01 m"
        elif defaultUnits == "in":
            unit_abbrev = "in"
            spacing_default = "0.394 in"  # ~10mm in inches
            thickness_default = "0.394 in"  # ~10mm in inches
        elif defaultUnits == "ft":
            unit_abbrev = "ft"
            spacing_default = "0.0328 ft"  # ~10mm in feet
            thickness_default = "0.0328 ft"  # ~10mm in feet
        else:
            # Fallback to mm if unknown unit
            unit_abbrev = "mm"
            spacing_default = "10 mm"
            thickness_default = "10 mm"

        inputs.addValueInput('spacing',      'Spacing',         defaultUnits,
                            adsk.core.ValueInput.createByString(spacing_default))
        inputs.addValueInput('thickness',    'Thickness',       defaultUnits,
                            adsk.core.ValueInput.createByString(thickness_default))
        inputs.addValueInput('inset_start',  'Inset at Start',  defaultUnits,
                            adsk.core.ValueInput.createByString(f"0 {unit_abbrev}"))
        inputs.addValueInput('inset_end',    'Inset at End',    defaultUnits,
                            adsk.core.ValueInput.createByString(f"0 {unit_abbrev}"))

        # 5) Centralization
        cen = inputs.addDropDownCommandInput('centralization','Centralization',
                     adsk.core.DropDownStyles.TextListDropDownStyle)
        cen.listItems.add('Slice', True)
        cen.listItems.add('Center', False)

        # 6) Add references
        ref = inputs.addDropDownCommandInput('add_references','Add References?',
                     adsk.core.DropDownStyles.TextListDropDownStyle)
        ref.listItems.add('Yes', True)
        ref.listItems.add('No', False)

        # 7) Text height
        inputs.addValueInput('text_height','Text Height', defaultUnits,
                     adsk.core.ValueInput.createByString(f"0.04 {unit_abbrev}"))

        # 8) Flatten?
        flat = inputs.addDropDownCommandInput('flatten','Flatten?',
                      adsk.core.DropDownStyles.TextListDropDownStyle)
        flat.listItems.add('Yes', True)
        flat.listItems.add('No', False)

        # 9) Export to DXF?
        inputs.addBoolValueInput(
        'export_dxf',
        'Export Slices to DXF',
        True,     # <-- now this is a checkbox
        '',
        False     # default unchecked
         )

        # NOW SET VISIBILITY - After all inputs are created
        # Initially hide radial-specific controls (since Y-axis is selected by default)
        inputs.itemById('radial_spacing').isVisible = False
        inputs.itemById('radial_start_angle').isVisible = False
        inputs.itemById('radial_end_angle').isVisible = False
        inputs.itemById('radial_center_axis').isVisible = False

        # Show linear spacing controls by default
        inputs.itemById('spacing').isVisible = True
        inputs.itemById('thickness').isVisible = True
        inputs.itemById('inset_start').isVisible = True
        inputs.itemById('inset_end').isVisible = True
        inputs.itemById('centralization').isVisible = True

        # Events
        futil.add_handler(args.command.execute,        command_execute,       local_handlers=local_handlers)
        futil.add_handler(args.command.inputChanged,   command_input_changed, local_handlers=local_handlers)
        futil.add_handler(args.command.validateInputs, command_validate_input,local_handlers=local_handlers)
        futil.add_handler(args.command.destroy,        command_destroy,       local_handlers=local_handlers)

    except:
        futil.handle_error('command_created', True)

def detect_center_axis(body):
    """
    Detect the center axis of a cylindrical or toroidal body
    Returns: (center_point, axis_direction, radius) or None if not detectable
    """
    try:
        # Get the bounding box to find the center
        bb = body.boundingBox
        center = adsk.core.Point3D.create(
            (bb.minPoint.x + bb.maxPoint.x) * 0.5,
            (bb.minPoint.y + bb.maxPoint.y) * 0.5,
            (bb.minPoint.z + bb.maxPoint.z) * 0.5
        )
        
        # Try to find cylindrical or toroidal faces
        for face in body.faces:
            if face.geometry.surfaceType == adsk.core.SurfaceTypes.CylinderSurfaceType:
                cyl = adsk.core.Cylinder.cast(face.geometry)
                return cyl.origin, cyl.axis, cyl.radius
            elif face.geometry.surfaceType == adsk.core.SurfaceTypes.TorusSurfaceType:
                torus = adsk.core.Torus.cast(face.geometry)
                return torus.origin, torus.axis, torus.majorRadius
        
        # Fallback: assume Z-axis through bounding box center
        z_axis = adsk.core.Vector3D.create(0, 0, 1)
        max_dim = max(bb.maxPoint.x - bb.minPoint.x, bb.maxPoint.y - bb.minPoint.y)
        return center, z_axis, max_dim * 0.5
        
    except Exception as e:
        futil.log(f'Error detecting center axis: {str(e)}')
        return None



def create_radial_slices(body0, root_comp, in_place_comp, flattened_comp, 
                        angular_spacing, start_angle, end_angle, center_axis_selection,
                        add_references, export_dxf, outFolder, flatten):
    """
    Create radial slices of a body by creating intersection sketches on radial planes
    """
    slice_count = 0
    slice_bodies_for_flattening = []
    
    try:
        # Detect or set the center axis
        if center_axis_selection == 'Auto-detect':
            axis_info = detect_center_axis(body0)
            if axis_info:
                center_point, axis_direction, radius = axis_info
            else:
                ui.messageBox("Could not auto-detect center axis. Please select manually.")
                return slice_count, slice_bodies_for_flattening
        else:
            # Use bounding box center with specified axis
            bb = body0.boundingBox
            center_point = adsk.core.Point3D.create(
                (bb.minPoint.x + bb.maxPoint.x) * 0.5,
                (bb.minPoint.y + bb.maxPoint.y) * 0.5,
                (bb.minPoint.z + bb.maxPoint.z) * 0.5
            )
            
            if center_axis_selection == 'X-axis':
                axis_direction = adsk.core.Vector3D.create(1, 0, 0)
            elif center_axis_selection == 'Y-axis':
                axis_direction = adsk.core.Vector3D.create(0, 1, 0)
            else:  # Z-axis
                axis_direction = adsk.core.Vector3D.create(0, 0, 1)

        futil.log("Creating radial slices using direct plane intersection method...")
        
        # Calculate number of slices
        total_angle = end_angle - start_angle
        num_slices = max(1, int(total_angle / angular_spacing))
        actual_angular_spacing = total_angle / num_slices
        
        futil.log(f"Creating {num_slices} radial slices with {actual_angular_spacing}° spacing")
        
        # Get body dimensions for determining slice thickness
        bb = body0.boundingBox
        max_dim = max(
            bb.maxPoint.x - bb.minPoint.x,
            bb.maxPoint.y - bb.minPoint.y,
            bb.maxPoint.z - bb.minPoint.z
        )
        slice_thickness = max_dim * 0.02  # 2% of the largest dimension
        
        for i in range(num_slices):
            try:
                slice_count += 1
                current_angle = start_angle + i * actual_angular_spacing
                
                futil.log(f'Creating radial slice {slice_count} at angle {current_angle}°')
                
                # Create radial plane using existing construction planes
                plane_normal = calculate_radial_plane_normal(axis_direction, current_angle)
                
                # Use appropriate base construction plane
                if abs(axis_direction.z) > 0.9:  # Z-axis
                    base_plane = root_comp.xYConstructionPlane
                elif abs(axis_direction.x) > 0.9:  # X-axis
                    base_plane = root_comp.yZConstructionPlane
                else:  # Y-axis
                    base_plane = root_comp.xZConstructionPlane
                
                # Create offset plane through the center point
                body_parent = body0.parentComponent
                planes = body_parent.constructionPlanes
                plane_input = planes.createInput()
                
                # Calculate offset from base plane to center point
                if abs(axis_direction.z) > 0.9:  # Z-axis
                    offset = center_point.z
                elif abs(axis_direction.x) > 0.9:  # X-axis
                    offset = center_point.x
                else:  # Y-axis
                    offset = center_point.y
                
                plane_input.setByOffset(base_plane, adsk.core.ValueInput.createByReal(offset))
                center_plane = planes.add(plane_input)
                
                # Create sketch on this plane
                sk = in_place_comp.sketches.add(center_plane)
                
                # Create radial cutting line on the sketch
                lines = sk.sketchCurves.sketchLines
                
                # Calculate line endpoints for the radial cut
                line_length = max_dim  # Make line longer than the object
                angle_rad = math.radians(current_angle)
                
                if abs(axis_direction.z) > 0.9:  # Z-axis
                    start_pt = adsk.core.Point3D.create(
                        center_point.x - center_point.x,  # Relative to sketch origin
                        center_point.y - center_point.y,  # Relative to sketch origin
                        0
                    )
                    end_pt = adsk.core.Point3D.create(
                        line_length * math.cos(angle_rad),
                        line_length * math.sin(angle_rad),
                        0
                    )
                elif abs(axis_direction.x) > 0.9:  # X-axis
                    start_pt = adsk.core.Point3D.create(
                        0,
                        center_point.y - center_point.y,
                        center_point.z - center_point.z
                    )
                    end_pt = adsk.core.Point3D.create(
                        0,
                        line_length * math.cos(angle_rad),
                        line_length * math.sin(angle_rad)
                    )
                else:  # Y-axis
                    start_pt = adsk.core.Point3D.create(
                        center_point.x - center_point.x,
                        0,
                        center_point.z - center_point.z
                    )
                    end_pt = adsk.core.Point3D.create(
                        line_length * math.cos(angle_rad),
                        0,
                        line_length * math.sin(angle_rad)
                    )
                
                # Create the radial line
                radial_line = lines.addByTwoPoints(start_pt, end_pt)
                
                # Intersect with the body to get the cross-section
                curves = sk.intersectWithSketchPlane([body0])
                
                if curves and sk.profiles.count > 0:
                    futil.log(f'Successfully created intersection for radial slice {slice_count}')
                    
                    # Add reference text if requested
                    if add_references:
                        add_radial_reference_text(sk, slice_count, current_angle)
                    
                    # Export DXF if requested
                    if export_dxf and outFolder:
                        export_radial_slice_dxf(sk, outFolder, slice_count, current_angle)
                    
                    # Create 3D slice body from the largest profile
                    largest_profile = get_largest_profile(sk)
                    if largest_profile:
                        slice_body = create_extruded_slice_body(in_place_comp, largest_profile, 
                                                              slice_thickness, slice_count)
                        
                        if slice_body:
                            slice_body.name = f"RadialSlice_{slice_count:03d}"
                            
                            # Add to flattening list if needed
                            if flatten and flattened_comp:
                                slice_bodies_for_flattening.append((slice_body, slice_count, current_angle))
                
                # Clean up
                sk.deleteMe()
                center_plane.deleteMe()
                
            except Exception as slice_error:
                futil.log(f'Error creating radial slice {slice_count}: {str(slice_error)}')
                # Continue with next slice
                
    except Exception as e:
        futil.log(f'Error in radial slicing: {str(e)}')
    
    return slice_count, slice_bodies_for_flattening

def get_largest_profile(sketch):
    """
    Get the largest profile from a sketch (typically the main cross-section)
    """
    if sketch.profiles.count == 0:
        return None
    
    largest_profile = None
    largest_area = 0
    
    for i in range(sketch.profiles.count):
        profile = sketch.profiles.item(i)
        try:
            # Simple area estimation based on bounding box
            bb = profile.boundingBox
            area = (bb.maxPoint.x - bb.minPoint.x) * (bb.maxPoint.y - bb.minPoint.y)
            if area > largest_area:
                largest_area = area
                largest_profile = profile
        except:
            continue
    
    return largest_profile

def create_extruded_slice_body(component, profile, thickness, slice_number):
    """
    Create a 3D slice body by extruding a profile
    """
    try:
        profiles = adsk.core.ObjectCollection.create()
        profiles.add(profile)
        
        extrudes = component.features.extrudeFeatures
        extrude_input = extrudes.createInput(profiles, adsk.fusion.FeatureOperations.NewBodyFeatureOperation)
        
        # Extrude with specified thickness
        half_thickness = thickness * 0.5
        thickness_value = adsk.core.ValueInput.createByReal(half_thickness)
        extrude_input.setSymmetricExtent(thickness_value, True)
        
        extrude_result = extrudes.add(extrude_input)
        
        if extrude_result.bodies.count > 0:
            body = extrude_result.bodies.item(0)
            body.name = f"RadialSlice_{slice_number:03d}"
            futil.log(f'Created extruded slice body: RadialSlice_{slice_number:03d}')
            return body
        
        return None
        
    except Exception as e:
        futil.log(f'Error creating extruded slice body: {str(e)}')
        return None


def flatten_radial_slices(slice_bodies_for_flattening, flattened_comp, body0, add_references):
    """
    Flatten radial slices with proper spacing and positioning
    """
    if not slice_bodies_for_flattening or not flattened_comp:
        return
    
    futil.log(f'Flattening {len(slice_bodies_for_flattening)} radial slices...')
    
    # Calculate spacing and starting position
    bb_original = body0.boundingBox
    gap_distance = 10.0  # Gap between original and flattened slices
    grid_spacing = 5.0   # Spacing between flattened slices
    
    # Start position for flattened slices
    current_x = bb_original.maxPoint.x + gap_distance
    current_y = bb_original.minPoint.y
    
    move_feats = flattened_comp.features.moveFeatures
    
    for i, (body, slice_num, angle) in enumerate(slice_bodies_for_flattening):
        try:
            # Create a copy of the slice in the flattened component
            # First, get the intersection profile
            bb_original_slice = body.boundingBox
            center_z = (bb_original_slice.minPoint.z + bb_original_slice.maxPoint.z) * 0.5
            
            # Create a sketch at the center Z level
            flat_plane_input = flattened_comp.constructionPlanes.createInput()
            flat_plane_input.setByOffset(flattened_comp.xYConstructionPlane, 
                                       adsk.core.ValueInput.createByReal(center_z))
            flat_plane = flattened_comp.constructionPlanes.add(flat_plane_input)
            
            flat_sketch = flattened_comp.sketches.add(flat_plane)
            flat_curves = flat_sketch.intersectWithSketchPlane([body])
            
            if flat_curves and flat_sketch.profiles.count > 0:
                # Get the largest profile
                largest_profile = get_largest_profile(flat_sketch)
                if largest_profile:
                    # Create extruded body in flattened component
                    profiles = adsk.core.ObjectCollection.create()
                    profiles.add(largest_profile)
                    
                    extrudes = flattened_comp.features.extrudeFeatures
                    extrude_input = extrudes.createInput(profiles, adsk.fusion.FeatureOperations.NewBodyFeatureOperation)
                    
                    # Use thin extrude for flattened slices
                    thickness = 1.0  # Thin slice for flattened version
                    thickness_input = adsk.core.ValueInput.createByReal(thickness)
                    extrude_input.setDistanceExtent(False, thickness_input)
                    
                    extrude_result = extrudes.add(extrude_input)
                    
                    if extrude_result.bodies.count > 0:
                        flattened_body = extrude_result.bodies.item(0)
                        flattened_body.name = f"RadialSlice_Flat_{slice_num:03d}"
                        
                        # Get dimensions for positioning
                        bb_flat = flattened_body.boundingBox
                        width = bb_flat.maxPoint.x - bb_flat.minPoint.x
                        height = bb_flat.maxPoint.y - bb_flat.minPoint.y
                        
                        # Position the flattened slice
                        move_collection = adsk.core.ObjectCollection.create()
                        move_collection.add(flattened_body)
                        
                        transform = adsk.core.Matrix3D.create()
                        transform.translation = adsk.core.Vector3D.create(
                            current_x - bb_flat.minPoint.x,
                            current_y - bb_flat.minPoint.y,
                            -bb_flat.minPoint.z  # Flatten to Z=0
                        )
                        
                        move_input = move_feats.createInput(move_collection, transform)
                        move_feats.add(move_input)
                        
                        # Add reference text to flattened slice
                        if add_references:
                            add_flattened_radial_text(flattened_comp, flattened_body, slice_num, angle)
                        
                        futil.log(f'Flattened radial slice {slice_num} at position ({current_x:.2f}, {current_y:.2f})')
                        
                        # Update position for next slice
                        current_x += width + grid_spacing
                        
                        # Wrap to next row if needed
                        max_x = bb_original.maxPoint.x + gap_distance + 5 * (width + grid_spacing)
                        if current_x > max_x:
                            current_x = bb_original.maxPoint.x + gap_distance
                            current_y += height + grid_spacing
            
            # Clean up
            flat_sketch.deleteMe()
            flat_plane.deleteMe()
            
        except Exception as e:
            futil.log(f'Error flattening radial slice {slice_num}: {str(e)}')


def add_flattened_radial_text(component, body, slice_num, angle):
    """
    Add reference text to flattened radial slice
    """
    try:
        bb = body.boundingBox
        
        # Create text plane above the body
        planes = component.constructionPlanes
        plane_input = planes.createInput()
        text_z = bb.maxPoint.z + 0.1
        plane_input.setByOffset(component.xYConstructionPlane, 
                               adsk.core.ValueInput.createByReal(text_z))
        text_plane = planes.add(plane_input)
        
        # Create sketch for text
        text_sketch = component.sketches.add(text_plane)
        
        # Calculate text properties
        body_width = bb.maxPoint.x - bb.minPoint.x
        body_height = bb.maxPoint.y - bb.minPoint.y
        text_height = max(body_width, body_height) * 0.15
        
        text_center_x = (bb.minPoint.x + bb.maxPoint.x) * 0.5
        text_center_y = (bb.minPoint.y + bb.maxPoint.y) * 0.5
        
        text_label = f"R{slice_num:03d}_{int(angle):03d}"
        
        # Create text
        texts = text_sketch.sketchTexts
        txt_input = texts.createInput2(text_label, text_height)
        
        if txt_input:
            estimated_width = len(text_label) * 0.6 * text_height
            insert_pt = adsk.core.Point3D.create(
                text_center_x - estimated_width * 0.5,
                text_center_y - text_height * 0.5,
                0
            )
            corner2 = adsk.core.Point3D.create(
                text_center_x + estimated_width * 0.5,
                text_center_y + text_height * 0.5,
                0
            )
            
            result = txt_input.setAsMultiLine(
                insert_pt, corner2,
                adsk.core.HorizontalAlignments.CenterHorizontalAlignment,
                adsk.core.VerticalAlignments.MiddleVerticalAlignment,
                0
            )
            
            if result:
                texts.add(txt_input)
                futil.log(f'Added text "{text_label}" to flattened radial slice')
        
        # Clean up plane
        text_plane.deleteMe()
        
    except Exception as e:
        futil.log(f'Error adding text to flattened radial slice: {str(e)}')


def calculate_radial_plane_normal(axis_direction, angle_degrees):
    """
    Calculate the normal vector for a radial cutting plane
    """
    angle_rad = math.radians(angle_degrees)
    
    # Create a normal vector perpendicular to the axis
    if abs(axis_direction.z) > 0.9:  # Z-axis
        normal = adsk.core.Vector3D.create(
            math.cos(angle_rad),
            math.sin(angle_rad),
            0
        )
    elif abs(axis_direction.x) > 0.9:  # X-axis
        normal = adsk.core.Vector3D.create(
            0,
            math.cos(angle_rad),
            math.sin(angle_rad)
        )
    else:  # Y-axis
        normal = adsk.core.Vector3D.create(
            math.cos(angle_rad),
            0,
            math.sin(angle_rad)
        )
    
    return normal


def create_radial_cutting_plane(component, center_point, normal, slice_number):
    """
    Create a construction plane for radial cutting
    """
    try:
        planes = component.constructionPlanes
        plane_input = planes.createInput()
        plane_input.setByPlane(adsk.core.Plane.create(center_point, normal))
        cutting_plane = planes.add(plane_input)
        cutting_plane.name = f"RadialCuttingPlane_{slice_number}"
        return cutting_plane
    except Exception as e:
        futil.log(f'Error creating cutting plane: {str(e)}')
        return None


def create_slice_body_from_sketch(component, sketch, original_body, axis_direction):
    """
    Create a 3D slice body by extruding the intersection sketch and intersecting with original body
    """
    try:
        if sketch.profiles.count == 0:
            return None
        
        # Get the largest profile (main cross-section)
        largest_profile = None
        largest_area = 0
        
        for i in range(sketch.profiles.count):
            profile = sketch.profiles.item(i)
            # Simple area estimation based on bounding box
            bb = profile.boundingBox
            area = (bb.maxPoint.x - bb.minPoint.x) * (bb.maxPoint.y - bb.minPoint.y)
            if area > largest_area:
                largest_area = area
                largest_profile = profile
        
        if not largest_profile:
            return None
        
        # Create collection with the main profile
        profiles = adsk.core.ObjectCollection.create()
        profiles.add(largest_profile)
        
        # Get original body dimensions for extrude distance
        bb = original_body.boundingBox
        max_dimension = max(
            bb.maxPoint.x - bb.minPoint.x,
            bb.maxPoint.y - bb.minPoint.y,
            bb.maxPoint.z - bb.minPoint.z
        )
        
        # Extrude the profile through the entire body
        extrudes = component.features.extrudeFeatures
        extrude_input = extrudes.createInput(profiles, adsk.fusion.FeatureOperations.NewBodyFeatureOperation)
        
        # Extrude symmetrically to ensure we capture the entire body
        extrude_distance = adsk.core.ValueInput.createByReal(max_dimension)
        extrude_input.setSymmetricExtent(extrude_distance, True)
        
        extrude_result = extrudes.add(extrude_input)
        
        if extrude_result.bodies.count > 0:
            extruded_body = extrude_result.bodies.item(0)
            
            # Now intersect the extruded body with the original body to get the slice
            target_bodies = adsk.core.ObjectCollection.create()
            target_bodies.add(extruded_body)
            
            tool_bodies = adsk.core.ObjectCollection.create()
            tool_bodies.add(original_body)
            
            combine_features = component.features.combineFeatures
            combine_input = combine_features.createInput(target_bodies, tool_bodies)
            combine_input.operation = adsk.fusion.FeatureOperations.IntersectFeatureOperation
            combine_input.isKeepToolBodies = True  # Keep the original body
            
            combine_result = combine_features.add(combine_input)
            
            if combine_result.bodies.count > 0:
                return combine_result.bodies.item(0)
        
        return None
        
    except Exception as e:
        futil.log(f'Error creating slice body from sketch: {str(e)}')
        return None


def add_radial_reference_text(sketch, slice_count, angle):
    """
    Add reference text to radial slice sketch
    """
    try:
        sk_bb = sketch.boundingBox
        sketch_diameter = max(sk_bb.maxPoint.x - sk_bb.minPoint.x,
                            sk_bb.maxPoint.y - sk_bb.minPoint.y)
        text_height = sketch_diameter * 0.1
        
        text_center_x = (sk_bb.minPoint.x + sk_bb.maxPoint.x) * 0.5
        text_center_y = (sk_bb.minPoint.y + sk_bb.maxPoint.y) * 0.5
        
        text_label = f"R{slice_count:03d}_{int(angle):03d}"
        
        texts = sketch.sketchTexts
        txt_input = texts.createInput2(text_label, text_height)
        
        if txt_input:
            estimated_width = len(text_label) * 0.6 * text_height
            insert_pt = adsk.core.Point3D.create(
                text_center_x - estimated_width * 0.5,
                text_center_y - text_height * 0.5,
                0
            )
            corner2 = adsk.core.Point3D.create(
                text_center_x + estimated_width * 0.5,
                text_center_y + text_height * 0.5,
                0
            )
            
            result = txt_input.setAsMultiLine(
                insert_pt, corner2,
                adsk.core.HorizontalAlignments.CenterHorizontalAlignment,
                adsk.core.VerticalAlignments.MiddleVerticalAlignment,
                0
            )
            
            if result:
                texts.add(txt_input)
                futil.log(f'Added text "{text_label}" to radial slice')
                
    except Exception as text_error:
        futil.log(f'Error adding text to radial slice: {str(text_error)}')


def export_radial_slice_dxf(sketch, out_folder, slice_count, angle):
    """
    Export radial slice sketch to DXF
    """
    try:
        filename = os.path.join(out_folder, f"RadialSlice_{slice_count:03d}_{int(angle):03d}.dxf")
        if hasattr(sketch, 'saveAsDXF'):
            sketch.saveAsDXF(filename)
        elif hasattr(sketch, 'exportToDXF'):
            sketch.exportToDXF(filename)
        futil.log(f'Exported radial DXF: {filename}')
    except Exception as e:
        futil.log(f'Error exporting radial DXF: {str(e)}')



def create_wedge_tool(component, center_point, axis_direction, start_angle, end_angle, bounding_box):
    """
    Create a wedge-shaped tool body for cutting radial slices
    """
    try:
        # Calculate wedge dimensions based on bounding box
        bb_size = max(
            bounding_box.maxPoint.x - bounding_box.minPoint.x,
            bounding_box.maxPoint.y - bounding_box.minPoint.y,
            bounding_box.maxPoint.z - bounding_box.minPoint.z
        )
        radius = bb_size * 0.6  # Make wedge slightly larger than the object
        height = bb_size * 1.2
        
        # Create sketch for wedge profile
        sketch_plane = component.xYConstructionPlane
        if abs(axis_direction.z) < 0.9:  # Not Z-axis
            # Create appropriate plane for X or Y axis
            planes = component.constructionPlanes
            plane_input = planes.createInput()
            if abs(axis_direction.x) > 0.9:  # X-axis
                sketch_plane = component.yZConstructionPlane
            else:  # Y-axis  
                sketch_plane = component.xZConstructionPlane
        
        sketch = component.sketches.add(sketch_plane)
        
        # Convert angles to radians
        start_rad = math.radians(start_angle)
        end_rad = math.radians(end_angle)
        
        # Create wedge profile points
        lines = sketch.sketchCurves.sketchLines
        
        # Center point (origin)
        origin = adsk.core.Point3D.create(0, 0, 0)
        
        # Start radius point
        start_point = adsk.core.Point3D.create(
            radius * math.cos(start_rad),
            radius * math.sin(start_rad), 
            0
        )
        
        # End radius point  
        end_point = adsk.core.Point3D.create(
            radius * math.cos(end_rad),
            radius * math.sin(end_rad),
            0
        )
        
        # Create wedge lines
        line1 = lines.addByTwoPoints(origin, start_point)
        line2 = lines.addByTwoPoints(origin, end_point)
        
        # Create arc between the radius points
        arcs = sketch.sketchCurves.sketchArcs
        arc = arcs.addByCenterStartEnd(origin, start_point, end_point)
        
        # Extrude the wedge profile
        profiles = adsk.core.ObjectCollection.create()
        if sketch.profiles.count > 0:
            profiles.add(sketch.profiles.item(0))
            
            extrudes = component.features.extrudeFeatures
            extrude_input = extrudes.createInput(profiles, adsk.fusion.FeatureOperations.NewBodyFeatureOperation)
            
            # Extrude along the axis direction
            distance = adsk.core.ValueInput.createByReal(height)
            extrude_input.setDistanceExtent(False, distance)
            
            extrude_result = extrudes.add(extrude_input)
            
            if extrude_result.bodies.count > 0:
                wedge_body = extrude_result.bodies.item(0)
                
                # Move wedge to correct position
                if center_point.x != 0 or center_point.y != 0 or center_point.z != 0:
                    move_bodies = adsk.core.ObjectCollection.create()
                    move_bodies.add(wedge_body)
                    
                    transform = adsk.core.Matrix3D.create()
                    transform.translation = adsk.core.Vector3D.create(
                        center_point.x, center_point.y, center_point.z
                    )
                    
                    move_features = component.features.moveFeatures
                    move_input = move_features.createInput(move_bodies, transform)
                    move_features.add(move_input)
                
                return wedge_body
        
        # Cleanup sketch if wedge creation failed
        sketch.deleteMe()
        return None
        
    except Exception as e:
        futil.log(f'Error creating wedge tool: {str(e)}')
        return None


def create_radial_dxf_export(component, body, center_point, axis_direction, 
                           start_angle, end_angle, outFolder, add_references, slice_count):
    """
    Create DXF export for radial slice
    """
    try:
        # Create plane at middle angle for DXF cross-section
        mid_angle = (start_angle + end_angle) / 2
        mid_angle_rad = math.radians(mid_angle)
        
        if abs(axis_direction.z) > 0.9:  # Z-axis
            normal = adsk.core.Vector3D.create(
                math.cos(mid_angle_rad), 
                math.sin(mid_angle_rad), 
                0
            )
        else:
            normal = adsk.core.Vector3D.create(
                math.cos(mid_angle_rad),
                math.sin(mid_angle_rad), 
                0
            )
        
        # Create construction plane
        planes = component.constructionPlanes
        plane_input = planes.createInput()
        plane_input.setByPlane(adsk.core.Plane.create(center_point, normal))
        dxf_plane = planes.add(plane_input)
        
        # Create intersection sketch
        dxf_sketch = component.sketches.add(dxf_plane)
        curves = dxf_sketch.intersectWithSketchPlane([body])
        
        if curves and dxf_sketch.profiles.count > 0:
            # Add reference text if requested
            if add_references:
                try:
                    sk_bb = dxf_sketch.boundingBox
                    sketch_diameter = max(sk_bb.maxPoint.x - sk_bb.minPoint.x,
                                        sk_bb.maxPoint.y - sk_bb.minPoint.y)
                    text_height = sketch_diameter * 0.1
                    
                    text_center_x = (sk_bb.minPoint.x + sk_bb.maxPoint.x) * 0.5
                    text_center_y = (sk_bb.minPoint.y + sk_bb.maxPoint.y) * 0.5
                    
                    text_label = f"R{slice_count:03d}"
                    
                    texts = dxf_sketch.sketchTexts
                    txt_input = texts.createInput2(text_label, text_height)
                    
                    if txt_input:
                        estimated_width = len(text_label) * 0.6 * text_height
                        insert_pt = adsk.core.Point3D.create(
                            text_center_x - estimated_width * 0.5,
                            text_center_y - text_height * 0.5,
                            0
                        )
                        corner2 = adsk.core.Point3D.create(
                            text_center_x + estimated_width * 0.5,
                            text_center_y + text_height * 0.5,
                            0
                        )
                        
                        result = txt_input.setAsMultiLine(
                            insert_pt, corner2,
                            adsk.core.HorizontalAlignments.CenterHorizontalAlignment,
                            adsk.core.VerticalAlignments.MiddleVerticalAlignment,
                            0
                        )
                        
                        if result:
                            texts.add(txt_input)
                            futil.log(f'Added text "{text_label}" to radial DXF')
                            
                except Exception as text_error:
                    futil.log(f'Error adding text to radial DXF: {str(text_error)}')
            
            # Export DXF
            filename = os.path.join(outFolder, f"RadialSlice_{slice_count:03d}.dxf")
            if hasattr(dxf_sketch, 'saveAsDXF'):
                dxf_sketch.saveAsDXF(filename)
            elif hasattr(dxf_sketch, 'exportToDXF'):
                dxf_sketch.exportToDXF(filename)
            futil.log(f'Exported radial DXF: {filename}')
        
        # Cleanup
        dxf_sketch.deleteMe()
        dxf_plane.deleteMe()
        
    except Exception as e:
        futil.log(f'Error creating radial DXF export: {str(e)}')


def create_radial_planar_slices(body0, root_comp, in_place_comp, flattened_comp,
                               angular_spacing, start_angle, end_angle, center_axis_selection,
                               add_references, export_dxf, outFolder, flatten):
    """
    Fallback method: Create planar intersection slices (for DXF export mainly)
    """
    slice_count = 0
    slice_bodies_for_flattening = []
    
    futil.log("Using fallback planar intersection method for radial slicing")
    
    # This is similar to the original radial slicing code but cleaned up
    # Just creates the intersection sketches without trying to make 3D wedges
    
    return slice_count, slice_bodies_for_flattening



def command_execute(args: adsk.core.CommandEventArgs):
    futil.log(f'{CMD_NAME} Command Execute Event')
    try:
        inputs        = args.command.commandInputs
        sel_in        = inputs.itemById('body_selection')
        if sel_in.selectionCount == 0:
            ui.messageBox('A body must be selected.')
            return
        body0         = adsk.fusion.BRepBody.cast(sel_in.selection(0).entity)

        # Read parameters
        axis          = inputs.itemById('axis').selectedItem.name
        add_references= inputs.itemById('add_references').selectedItem.name == 'Yes'
        text_height   = inputs.itemById('text_height').value
        export_dxf    = inputs.itemById('export_dxf').value
        flatten       = inputs.itemById('flatten').selectedItem.name == 'Yes'

        design        = adsk.fusion.Design.cast(app.activeProduct)
        root_comp     = design.rootComponent

        # If DXF export is requested, prompt for folder now
        outFolder = None
        if export_dxf:
            fd = ui.createFolderDialog()
            fd.title = 'Select folder for DXF export'
            if fd.showDialog() == adsk.core.DialogResults.DialogOK:
                outFolder = fd.folder
            else:
                # Cancel export but continue slicing
                export_dxf = False

        # Create component for in-place slices
        in_place_occ = root_comp.occurrences.addNewComponent(adsk.core.Matrix3D.create())
        in_place_comp = in_place_occ.component
        in_place_comp.name = "Slices_InPlace"

        # Create component for flattened slices if needed
        flattened_comp = None
        if flatten:
            flattened_occ = root_comp.occurrences.addNewComponent(adsk.core.Matrix3D.create())
            flattened_comp = flattened_occ.component
            flattened_comp.name = "Slices_Flattened"

        # Hide original
        body0.isVisible = False

        slice_count  = 0
        slice_bodies_for_flattening = []

        if axis == 'Radial':
            # Read radial-specific parameters
            angular_spacing = inputs.itemById('radial_spacing').value
            start_angle = inputs.itemById('radial_start_angle').value  
            end_angle = inputs.itemById('radial_end_angle').value
            center_axis_selection = inputs.itemById('radial_center_axis').selectedItem.name
            
            # Create radial slices
            slice_count, slice_bodies_for_flattening = create_radial_slices(
                body0, root_comp, in_place_comp, flattened_comp,
                angular_spacing, start_angle, end_angle, center_axis_selection,
                add_references, export_dxf, outFolder, flatten
            )
            
            # Handle flattening for radial slices
            if flatten and slice_bodies_for_flattening and flattened_comp:
                flatten_radial_slices(slice_bodies_for_flattening, flattened_comp, body0, add_references)

        else:
            # Linear slicing (X, Y, Z axes)
            spacing       = inputs.itemById('spacing').value
            thickness     = inputs.itemById('thickness').value
            inset_s       = inputs.itemById('inset_start').value
            inset_e       = inputs.itemById('inset_end').value
            central       = inputs.itemById('centralization').selectedItem.name

            # Determine bounds and base plane
            bb = body0.boundingBox
            if axis == 'X':
                mn, mx, plane0 = bb.minPoint.x, bb.maxPoint.x, root_comp.yZConstructionPlane
            elif axis == 'Y':
                mn, mx, plane0 = bb.minPoint.y, bb.maxPoint.y, root_comp.xZConstructionPlane
            else:  # axis == 'Z'
                mn, mx, plane0 = bb.minPoint.z, bb.maxPoint.z, root_comp.xYConstructionPlane

            loop_start = mn + inset_s
            loop_end   = mx - inset_e
            if loop_start >= loop_end:
                ui.messageBox("Insets are too large; no slices possible.")
                return

            # Center if requested
            current = loop_start
            if central == 'Center':
                n = max(1, int((loop_end - loop_start) / (spacing + thickness)))
                used = n * thickness + (n - 1) * spacing if n > 1 else thickness
                current = loop_start + ((loop_end - loop_start) - used) * 0.5

            # Main linear slicing loop
            while current + thickness <= loop_end:
                mid_p = current + thickness * 0.5
                try:
                    # 1) build offset plane
                    planes   = body0.parentComponent.constructionPlanes
                    inp      = planes.createInput()
                    inp.setByOffset(plane0, adsk.core.ValueInput.createByReal(mid_p))
                    mid_plane= planes.add(inp)

                    # 2) sketch intersection
                    sk = in_place_comp.sketches.add(mid_plane)
                    curves = sk.intersectWithSketchPlane([body0])

                    if curves and sk.profiles.count > 0:
                        slice_count += 1

                        # Add reference text to the sketch BEFORE DXF export
                        if add_references:
                            try:
                                # Get sketch bounds to size and position text appropriately
                                sk_bb = sk.boundingBox
                                sketch_width = sk_bb.maxPoint.x - sk_bb.minPoint.x
                                sketch_height = sk_bb.maxPoint.y - sk_bb.minPoint.y
                                sketch_diameter = max(sketch_width, sketch_height)
                                
                                # Make text height proportional to sketch size (20% of diameter)
                                dxf_text_height = sketch_diameter * 0.2
                                
                                # Position text at center of the sketch
                                text_center_x = (sk_bb.minPoint.x + sk_bb.maxPoint.x) * 0.5
                                text_center_y = (sk_bb.minPoint.y + sk_bb.maxPoint.y) * 0.5
                                
                                # Build the label based on selected axis
                                prefix = axis[0].upper()
                                text_label = f"{prefix}{slice_count:03d}"
                                
                                # Estimate text width for centering
                                estimated_text_width = len(text_label) * 0.6 * dxf_text_height
                                
                                # Create text in the sketch
                                texts = sk.sketchTexts
                                txt_in = texts.createInput2(text_label, dxf_text_height)
                                
                                if txt_in:
                                    # Position text box centered on the sketch
                                    insert_pt = adsk.core.Point3D.create(
                                        text_center_x - estimated_text_width * 0.5,
                                        text_center_y - dxf_text_height * 0.5,
                                        0
                                    )
                                    
                                    corner2 = adsk.core.Point3D.create(
                                        text_center_x + estimated_text_width * 0.5,
                                        text_center_y + dxf_text_height * 0.5,
                                        0
                                    )
                                    
                                    # Set as multi-line text
                                    result = txt_in.setAsMultiLine(
                                        insert_pt,
                                        corner2,
                                        adsk.core.HorizontalAlignments.CenterHorizontalAlignment,
                                        adsk.core.VerticalAlignments.MiddleVerticalAlignment,
                                        0
                                    )
                                    
                                    if result:
                                        text_obj = texts.add(txt_in)
                                        if text_obj:
                                            futil.log(f'Added text "{text_label}" to sketch for DXF export')
                                    
                            except Exception as text_error:
                                futil.log(f'Error adding text to sketch for slice {slice_count}: {str(text_error)}')
                                # Continue with DXF export even if text fails

                        # 3) DXF export of the sketch (now with text if enabled)
                        if export_dxf and outFolder:
                            fname = os.path.join(outFolder, f"Slice_{slice_count}.dxf")
                            if hasattr(sk, 'saveAsDXF'):
                                sk.saveAsDXF(fname)
                            elif hasattr(sk, 'exportToDXF'):
                                sk.exportToDXF(fname)
                            futil.log(f'Exported Slice_{slice_count} DXF to {fname}')

                        # 4) extrude the profiles into bodies for in-place component
                        profs = adsk.core.ObjectCollection.create()
                        for i in range(sk.profiles.count):
                            profs.add(sk.profiles.item(i))

                        ext_feats = in_place_comp.features.extrudeFeatures
                        ext_in = ext_feats.createInput(profs, adsk.fusion.FeatureOperations.NewBodyFeatureOperation)
                        half_vi = adsk.core.ValueInput.createByReal(thickness * 0.5)
                        ext_in.setSymmetricExtent(half_vi, True)
                        # Set the extrude to maintain the exact profile shape
                        if hasattr(ext_in, 'isSolid'):
                            ext_in.isSolid = True
                        extrude = ext_feats.add(ext_in)

                        for b in extrude.bodies:
                            b.name = f"Slice_{slice_count}"
                            futil.log(f'Created in-place Slice_{slice_count} body')

                        # 5) If flattening is enabled, create duplicate bodies for flattening
                        if flatten and flattened_comp:
                            sk_flat = flattened_comp.sketches.add(mid_plane)
                            curves_flat = sk_flat.intersectWithSketchPlane([body0])
                            
                            if curves_flat and sk_flat.profiles.count > 0:
                                profs_flat = adsk.core.ObjectCollection.create()
                                for i in range(sk_flat.profiles.count):
                                    profs_flat.add(sk_flat.profiles.item(i))

                                ext_feats_flat = flattened_comp.features.extrudeFeatures
                                ext_in_flat = ext_feats_flat.createInput(profs_flat, 
                                                    adsk.fusion.FeatureOperations.NewBodyFeatureOperation)
                                ext_in_flat.setSymmetricExtent(half_vi, True)
                                # Set the extrude to maintain the exact profile shape for flattened slices too
                                if hasattr(ext_in_flat, 'isSolid'):
                                    ext_in_flat.isSolid = True
                                extrude_flat = ext_feats_flat.add(ext_in_flat)

                                for b in extrude_flat.bodies:
                                    b.name = f"Slice_{slice_count}"
                                    slice_bodies_for_flattening.append((b, slice_count))
                            
                            sk_flat.deleteMe()

                    # cleanup
                    sk.deleteMe()
                    mid_plane.deleteMe()

                except:
                    futil.log(f'Error slicing at {mid_p}:\n{traceback.format_exc()}')

                current += spacing + thickness

            # Flatten linear slices (only the ones in the flattened component)
            if flatten and slice_bodies_for_flattening and flattened_comp:
                futil.log(f'Flattening {len(slice_bodies_for_flattening)} linear slices…')
                
                # spacing between slices (in the same units as your model!)
                grid_spacing = 2.0   
                
                # Calculate initial offset based on the original body's bounding box
                # This creates separation between in-place and flattened slices
                bb_original = body0.boundingBox
                gap_distance = 10.0  # Adjust this value to increase/decrease the gap
                
                # Calculate starting position based on axis and add gap
                if axis == 'Z':
                    # For Z-axis slicing, offset in X direction
                    current_x = bb_original.maxPoint.x + gap_distance
                elif axis == 'X':
                    # For X-axis slicing, offset in Y direction (which becomes X after rotation)
                    current_x = bb_original.maxPoint.y + gap_distance
                else:  # axis == 'Y'
                    # For Y-axis slicing, offset in X direction
                    current_x = bb_original.maxPoint.x + gap_distance
                
                move_feats = flattened_comp.features.moveFeatures
                for (body, slice_num) in slice_bodies_for_flattening:
                    try:
                        # capture the bounding box before we move anything
                        bb = body.boundingBox
                        width  = bb.maxPoint.x - bb.minPoint.x
                        height = bb.maxPoint.y - bb.minPoint.y
                        
                        # 1) Rotate into the XY plane (only if slicing on X or Y)
                        if axis != 'Z':
                            coll = adsk.core.ObjectCollection.create()
                            coll.add(body)
                            rot = adsk.core.Matrix3D.create()
                            if axis == 'X':
                                rot.setToRotation(math.pi/2,
                                    adsk.core.Vector3D.create(0,1,0),
                                    adsk.core.Point3D.create(0,0,0))
                            else:  # axis == 'Y'
                                rot.setToRotation(math.pi/2,
                                    adsk.core.Vector3D.create(1,0,0),
                                    adsk.core.Point3D.create(0,0,0))
                            move_feats.add(move_feats.createInput(coll, rot))

                        # 2) Translate so the slice's min-corner sits at (current_x, 0, 0)
                        coll2 = adsk.core.ObjectCollection.create()
                        coll2.add(body)
                        trans = adsk.core.Matrix3D.create()
                        trans.translation = adsk.core.Vector3D.create(
                            current_x - bb.minPoint.x,    # X so minX→current_x
                            -bb.minPoint.y,              # Y so minY→0
                            -bb.minPoint.z               # Z so minZ→0
                        )
                        move_feats.add(move_feats.createInput(coll2, trans))

                        # 3) Optionally add reference text on flattened slices
                        if add_references:
                            try:
                                # Get the bounding box of the moved slice
                                bb_slice = body.boundingBox
                                
                                # Create a construction plane at the top of the slice
                                planes = flattened_comp.constructionPlanes
                                plane_input = planes.createInput()
                                
                                # Create plane at the top Z level of the slice, parallel to XY
                                top_z = bb_slice.maxPoint.z + 0.001  # Slightly above the slice
                                plane_input.setByOffset(
                                    flattened_comp.xYConstructionPlane, 
                                    adsk.core.ValueInput.createByReal(top_z)
                                )
                                text_plane = planes.add(plane_input)
                                
                                # Create sketch on this construction plane
                                text_sk = flattened_comp.sketches.add(text_plane)
                                texts = text_sk.sketchTexts
                                
                                # Calculate slice dimensions
                                slice_width = bb_slice.maxPoint.x - bb_slice.minPoint.x
                                slice_height = bb_slice.maxPoint.y - bb_slice.minPoint.y
                                slice_diameter = max(slice_width, slice_height)
                                
                                # Make text height proportional to slice size (e.g., 20% of diameter)
                                large_text_height = slice_diameter * 0.2
                                
                                # Position text at center of the slice (in XY projection)
                                text_center_x = (bb_slice.minPoint.x + bb_slice.maxPoint.x) * 0.5
                                text_center_y = (bb_slice.minPoint.y + bb_slice.maxPoint.y) * 0.5
                                
                                # Build the label: e.g., "Z001", "Z002", etc., based on the selected axis
                                prefix = axis[0].upper()  # "X", "Y", or "Z" based on the selected axis
                                text_label = f"{prefix}{slice_num:03d}"
                                
                                # Estimate text width (rough approximation: characters * 0.6 * height)
                                estimated_text_width = len(text_label) * 0.6 * large_text_height
                                
                                # Calculate text box corners to center the text
                                insert_pt = adsk.core.Point3D.create(
                                    text_center_x - estimated_text_width * 0.5,  # Left edge
                                    text_center_y - large_text_height * 0.5,     # Bottom edge
                                    0  # Z=0 in the sketch plane coordinate system
                                )
                                
                                # Create text input using createInput2
                                txt_in = texts.createInput2(text_label, large_text_height)
                                
                                if txt_in:
                                    # Define the text area - make it large enough for the text
                                    corner2 = adsk.core.Point3D.create(
                                        text_center_x + estimated_text_width * 0.5,  # Right edge
                                        text_center_y + large_text_height * 0.5,     # Top edge
                                        0  # Z=0 in sketch coordinate system
                                    )
                                    
                                    # Set as multi-line text
                                    result = txt_in.setAsMultiLine(
                                        insert_pt,  # Bottom-left corner
                                        corner2,    # Top-right corner  
                                        adsk.core.HorizontalAlignments.CenterHorizontalAlignment,
                                        adsk.core.VerticalAlignments.MiddleVerticalAlignment,
                                        0  # Rotation angle in radians
                                    )
                                    
                                    if result:
                                        # Add the text to the sketch
                                        text_obj = texts.add(txt_in)
                                        if text_obj:
                                            # Now extrude the text downward to emboss it into the slice
                                            try:
                                                # Get all profiles from the text
                                                text_profiles = adsk.core.ObjectCollection.create()
                                                for i in range(text_sk.profiles.count):
                                                    text_profiles.add(text_sk.profiles.item(i))
                                                
                                                if text_profiles.count > 0:
                                                    # Create extrude feature to cut into the slice
                                                    extrudes = flattened_comp.features.extrudeFeatures
                                                    extrude_input = extrudes.createInput(
                                                        text_profiles, 
                                                        adsk.fusion.FeatureOperations.CutFeatureOperation
                                                    )
                                                    
                                                    # Extrude downward by a larger amount for more visible embossing
                                                    extrude_distance = large_text_height * 0.05  # 5% of the large text height
                                                    distance_input = adsk.core.ValueInput.createByReal(extrude_distance)
                                                    extrude_input.setDistanceExtent(False, distance_input)  # False = downward
                                                    
                                                    # Add the extrude feature
                                                    extrude_feature = extrudes.add(extrude_input)
                                                    
                                                    futil.log(f'Added embossed text "{text_label}" on slice {slice_num}')
                                                else:
                                                    futil.log(f'No text profiles found for slice {slice_num}')
                                                    
                                            except Exception as extrude_error:
                                                futil.log(f'Error extruding text for slice {slice_num}: {str(extrude_error)}')
                                                # Keep the text as sketch even if extrude fails
                                                futil.log(f'Added text sketch "{text_label}" for slice {slice_num}')
                                        else:
                                            futil.log(f'Failed to add text "{text_label}" for slice {slice_num}')
                                    else:
                                        futil.log(f'Failed to set text as multi-line for slice {slice_num}')
                                else:
                                    futil.log(f'Failed to create text input for slice {slice_num}')
                                                        
                            except Exception as e:
                                futil.log(f'Error adding text for slice {slice_num}: {str(e)}')

                        futil.log(f'Placed slice {slice_num} at X={current_x:.2f}')
                        
                        # 4) advance X for the next slice
                        current_x += width + grid_spacing

                    except:
                        futil.log(f'Error flattening slice {slice_num}:\n{traceback.format_exc()}')

        # Report
        total_in_place = in_place_comp.bRepBodies.count
        total_flattened = flattened_comp.bRepBodies.count if flattened_comp else 0
        
        if total_in_place > 0 or total_flattened > 0:
            msg = f'Successfully created {total_in_place} in-place slice(s)'
            if flatten and total_flattened > 0:
                msg += f' and {total_flattened} flattened slice(s)'
            if export_dxf:
                msg += ' + exported DXF'
            msg += '.'
            ui.messageBox(msg)
        else:
            ui.messageBox('No slices were created. Check parameters or logs.')

    except:
        ui.messageBox('Failed:\n{}'.format(traceback.format_exc()))
        futil.log(f'Command Execute Failed:\n{traceback.format_exc()}')



def command_input_changed(args: adsk.core.InputChangedEventArgs):
    futil.log(f'{CMD_NAME} Input Changed Event: {args.input.id}')
    
    inputs = args.inputs
    changed_input = args.input
    
    # Show/hide controls based on axis selection
    if changed_input.id == 'axis':
        axis_selection = changed_input.selectedItem.name
        
        # Linear axis controls
        linear_controls = ['spacing', 'thickness', 'inset_start', 'inset_end', 'centralization']
        # Radial axis controls  
        radial_controls = ['radial_spacing', 'radial_start_angle', 'radial_end_angle', 'radial_center_axis']
        
        if axis_selection == 'Radial':
            # Hide linear controls
            for control_id in linear_controls:
                control = inputs.itemById(control_id)
                if control:
                    control.isVisible = False
            
            # Show radial controls
            for control_id in radial_controls:
                control = inputs.itemById(control_id)
                if control:
                    control.isVisible = True
        else:
            # Show linear controls
            for control_id in linear_controls:
                control = inputs.itemById(control_id)
                if control:
                    control.isVisible = True
            
            # Hide radial controls
            for control_id in radial_controls:
                control = inputs.itemById(control_id)
                if control:
                    control.isVisible = False


def command_validate_input(args: adsk.core.ValidateInputsEventArgs):
    sel = args.inputs.itemById('body_selection')
    args.areInputsValid = (sel.selectionCount > 0)

def command_destroy(args: adsk.core.CommandEventArgs):
    futil.log(f'{CMD_NAME} Command Destroy Event')
    global local_handlers
    local_handlers = []